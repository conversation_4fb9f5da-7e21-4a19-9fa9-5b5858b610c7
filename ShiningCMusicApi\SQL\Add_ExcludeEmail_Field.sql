USE [MusicSchool]
GO

-- Add ExcludeEmail field to Students table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Students]') AND name = 'ExcludeEmail')
BEGIN
    ALTER TABLE [dbo].[Students]
    ADD [ExcludeEmail] [bit] NOT NULL DEFAULT 0
    
    PRINT 'ExcludeEmail field added to Students table successfully.'
END
ELSE
BEGIN
    PRINT 'ExcludeEmail field already exists in Students table.'
END
GO
