# Payment Reminder Service - Complete Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Features](#features)
4. [Configuration](#configuration)
5. [Database Schema](#database-schema)
6. [Installation Guide](#installation-guide)
7. [API Reference](#api-reference)
8. [Email Templates](#email-templates)
9. [Monitoring & Logging](#monitoring--logging)
10. [Troubleshooting](#troubleshooting)
11. [Testing](#testing)

## Overview

The Payment Reminder Service is an automated background service that sends email reminders to students when they have a low number of lessons remaining in their current term. This helps ensure timely payment collection and prevents lesson interruptions.

### Key Benefits
- **Automated Payment Reminders**: Reduces manual administrative work
- **Configurable Thresholds**: Customizable lesson count triggers
- **Email Opt-out Support**: Respects student preferences
- **Recurring Lesson Support**: Intelligent calculation for complex schedules
- **Professional Communication**: Branded email templates
- **Comprehensive Logging**: Full audit trail and monitoring

## Architecture

### System Components

```
┌─────────────────────────────────────────────────────────────┐
│                Payment Reminder Service                      │
├─────────────────────────────────────────────────────────────┤
│  Background Service (Hosted Service)                        │
│  ├── Configuration Management                               │
│  ├── Lesson Counting Logic                                  │
│  ├── Email Service Integration                              │
│  └── Logging & Monitoring                                   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Dependencies                             │
├─────────────────────────────────────────────────────────────┤
│  ├── LessonService (Lesson counting & recurrence logic)     │
│  ├── EmailService (Template-based email sending)           │
│  ├── StudentService (Student data & email preferences)     │
│  └── EmailTemplateService (Template management)            │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     Database                                │
├─────────────────────────────────────────────────────────────┤
│  ├── Students (with ExcludeEmail field)                    │
│  ├── Lessons (with recurrence support)                     │
│  └── EmailTemplates (PaymentReminder template)             │
└─────────────────────────────────────────────────────────────┘
```

### Service Flow

1. **Timer Trigger**: Service runs on configurable interval (default: 24 hours)
2. **Student Query**: Retrieves active students with email addresses (ExcludeEmail = false)
3. **Lesson Counting**: Calculates remaining lessons for each student
4. **Threshold Check**: Identifies students with lessons ≤ configured threshold
5. **Email Generation**: Creates personalized emails using template system
6. **Email Sending**: Sends reminders via existing email infrastructure
7. **Logging**: Records all activities for monitoring and audit

## Features

### Core Functionality

#### Intelligent Lesson Counting
- **Regular Lessons**: Counts future scheduled lessons
- **Recurring Lessons**: Calculates remaining occurrences based on:
  - UNTIL dates in RRULE specifications
  - COUNT values in RRULE specifications
  - Intelligent estimation for infinite series (3-month window)
- **Recurrence Patterns**: Supports DAILY, WEEKLY, MONTHLY, YEARLY with intervals

#### Email Management
- **Template-Based**: Uses existing email template system
- **Personalization**: Dynamic placeholders for student-specific information
- **Opt-out Support**: Respects ExcludeEmail student preference
- **Professional Design**: Branded HTML emails with fallback text versions

#### Configuration Flexibility
- **Environment Variables**: Production-ready configuration
- **appsettings.json**: Development and testing configuration
- **Runtime Adjustable**: No code changes required for configuration updates

### Advanced Features

#### Recurrence Rule Processing
The service processes RFC 5545 compliant recurrence rules:

```
FREQ=WEEKLY;INTERVAL=1;COUNT=10    → 10 weekly lessons
FREQ=DAILY;UNTIL=20241231T235959Z  → Daily until end date
FREQ=MONTHLY;INTERVAL=2            → Every 2 months (infinite)
```

#### Email Exclusion Logic
Students can be excluded from reminders via the `ExcludeEmail` field:
- Database field: `Students.ExcludeEmail` (bit, default: 0)
- UI Integration: Can be managed through student administration
- Immediate Effect: Changes take effect on next service run

## Configuration

### Environment Variables (Recommended for Production)

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `PAYMENT_REMINDER_INTERVAL_HOURS` | Service execution interval | 24 | 12 |
| `PAYMENT_REMINDER_LESSON_THRESHOLD` | Lessons remaining to trigger reminder | 3 | 5 |
| `PAYMENT_REMINDER_DEADLINE_DAYS` | Days from now for payment deadline | 7 | 14 |

### appsettings.json Configuration

```json
{
  "PaymentReminder": {
    "IntervalHours": 24,
    "LessonThreshold": 3,
    "PaymentDeadlineDays": 7
  }
}
```

### Configuration Priority
1. Environment Variables (highest priority)
2. appsettings.json values
3. Default values (fallback)

## Database Schema

### New Fields

#### Students Table
```sql
ALTER TABLE [dbo].[Students]
ADD [ExcludeEmail] [bit] NOT NULL DEFAULT 0
```

| Field | Type | Description |
|-------|------|-------------|
| ExcludeEmail | bit | When true, student won't receive payment reminders |

### Email Templates

#### PaymentReminder Template
```sql
INSERT INTO [dbo].[EmailTemplates] ([Name], [Subject], [BodyHtml], [BodyText])
VALUES ('PaymentReminder', 
        'Payment Reminder - {LessonsRemaining} Lessons Remaining - Shining C Music Studio',
        '<!-- HTML content with placeholders -->',
        '<!-- Text content with placeholders -->')
```

#### Template Placeholders
| Placeholder | Description | Example |
|-------------|-------------|---------|
| `{StudentName}` | Student's full name | "John Smith" |
| `{LessonsRemaining}` | Number of lessons remaining | "3" |
| `{PaymentDeadline}` | Formatted payment deadline | "December 15, 2024" |

## Installation Guide

### Prerequisites
- Shining C Music Studio API application
- SQL Server database with existing schema
- Email service configuration (SMTP settings)
- .NET 6+ runtime environment

### Step 1: Database Migration
```bash
# Add ExcludeEmail field to Students table
sqlcmd -S your-server -d MusicSchool -i "SQL/Add_ExcludeEmail_Field.sql"

# Add PaymentReminder email template
sqlcmd -S your-server -d MusicSchool -i "SQL/Add_PaymentReminder_Template.sql"
```

### Step 2: Verify Installation
```bash
# Run test script to verify setup
sqlcmd -S your-server -d MusicSchool -i "SQL/Test_PaymentReminder_Setup.sql"
```

### Step 3: Configure Service
Set environment variables or update appsettings.json:

**Environment Variables (Production):**
```bash
export PAYMENT_REMINDER_INTERVAL_HOURS=24
export PAYMENT_REMINDER_LESSON_THRESHOLD=3
export PAYMENT_REMINDER_DEADLINE_DAYS=7
```

**appsettings.json (Development):**
```json
{
  "PaymentReminder": {
    "IntervalHours": 24,
    "LessonThreshold": 3,
    "PaymentDeadlineDays": 7
  }
}
```

### Step 4: Deploy and Start
The service is automatically registered and will start with the application. No additional deployment steps required.

## API Reference

### ILessonService Extensions

#### GetRemainingLessonsForStudentAsync
```csharp
Task<int> GetRemainingLessonsForStudentAsync(int studentId)
```
Calculates remaining lessons for a specific student.

**Parameters:**
- `studentId`: Student identifier

**Returns:** Number of remaining lessons

#### GetStudentsWithRemainingLessonsAsync
```csharp
Task<IEnumerable<(int StudentId, string StudentName, string Email, int RemainingLessons)>> 
GetStudentsWithRemainingLessonsAsync(int lessonThreshold)
```
Retrieves students with lesson count at or below threshold.

**Parameters:**
- `lessonThreshold`: Maximum lesson count to include

**Returns:** Collection of student information with lesson counts

### IEmailService Extensions

#### SendEmailFromTemplateAsync
```csharp
Task<bool> SendEmailFromTemplateAsync(string templateName, string toEmail, 
                                    Dictionary<string, string>? placeholders = null)
```
Sends email using specified template with placeholder replacement.

**Parameters:**
- `templateName`: Email template name ("PaymentReminder")
- `toEmail`: Recipient email address
- `placeholders`: Key-value pairs for placeholder replacement

**Returns:** Success status

## Email Templates

### PaymentReminder Template Structure

#### HTML Version Features
- **Responsive Design**: Mobile-friendly layout
- **Professional Styling**: Consistent with studio branding
- **Clear Call-to-Action**: Prominent payment deadline information
- **Accessibility**: Proper contrast and readable fonts

#### Text Version Features
- **Plain Text Fallback**: For email clients without HTML support
- **Structured Content**: Clear sections and formatting
- **Complete Information**: All essential details included

### Template Customization

To modify the email template:

1. **Update Database Record:**
```sql
UPDATE [dbo].[EmailTemplates]
SET [Subject] = 'New Subject - {LessonsRemaining} Lessons Remaining',
    [BodyHtml] = '<!-- Updated HTML content -->',
    [BodyText] = '<!-- Updated text content -->'
WHERE [Name] = 'PaymentReminder'
```

2. **Test Changes:**
Use the test script to verify template updates.

## Monitoring & Logging

### Log Categories

#### Service Lifecycle
```
PaymentReminderService started.
PaymentReminderService configured with interval: 24 hours, lesson threshold: 3, payment deadline: 7 days
PaymentReminderService stopped.
```

#### Execution Logs
```
Starting payment reminder process...
Successfully sent 5 payment reminder(s).
No payment reminders needed at this time.
```

#### Individual Reminders
```
Payment reminder sent to student John Smith (<EMAIL>) with 2 lessons remaining.
Failed to send payment reminder to student Jane Doe (<EMAIL>).
```

#### Error Handling
```
Error occurred during payment reminder processing.
Failed to process payment reminders.
Error sending payment reminder to student John Smith (<EMAIL>).
```

### Monitoring Recommendations

#### Key Metrics to Track
- **Execution Frequency**: Service run intervals
- **Reminder Volume**: Number of reminders sent per execution
- **Success Rate**: Percentage of successful email deliveries
- **Error Rate**: Failed email attempts and reasons
- **Student Coverage**: Number of eligible students processed

#### Alerting Suggestions
- **Service Failures**: Alert on consecutive execution failures
- **High Error Rates**: Alert when email failure rate exceeds threshold
- **Configuration Issues**: Alert on missing templates or configuration

## Troubleshooting

### Common Issues

#### No Reminders Sent
**Symptoms:** Service runs but no emails are sent

**Possible Causes:**
1. No students meet criteria (lessons > threshold)
2. All students have ExcludeEmail = true
3. Students lack valid email addresses
4. Email template missing

**Resolution:**
```sql
-- Check student eligibility
SELECT StudentId, StudentName, Email, ExcludeEmail, IsArchived
FROM Students
WHERE IsArchived = 0 AND ExcludeEmail = 0 AND Email IS NOT NULL AND Email != ''

-- Verify email template exists
SELECT * FROM EmailTemplates WHERE Name = 'PaymentReminder'
```

#### Incorrect Lesson Counts
**Symptoms:** Students receive reminders with wrong lesson counts

**Possible Causes:**
1. Recurrence rule parsing errors
2. Archived lessons not filtered properly
3. Date calculation issues

**Resolution:**
1. Review lesson data for affected students
2. Check recurrence rule format compliance
3. Verify lesson archival status

#### Email Delivery Failures
**Symptoms:** Service logs email failures

**Possible Causes:**
1. SMTP configuration issues
2. Invalid email addresses
3. Email service rate limits

**Resolution:**
1. Verify email service configuration
2. Test email service with known good addresses
3. Check email service logs for detailed errors

### Diagnostic Queries

#### Student Eligibility Check
```sql
SELECT 
    s.StudentId,
    s.StudentName,
    s.Email,
    s.ExcludeEmail,
    s.IsArchived,
    COUNT(l.LessonId) as FutureLessons
FROM Students s
LEFT JOIN Lessons l ON s.StudentId = l.StudentId 
    AND l.IsArchived = 0 
    AND l.StartTime > GETDATE()
WHERE s.IsArchived = 0
GROUP BY s.StudentId, s.StudentName, s.Email, s.ExcludeEmail, s.IsArchived
ORDER BY s.StudentName
```

#### Lesson Count Verification
```sql
SELECT 
    l.StudentId,
    s.StudentName,
    l.StartTime,
    l.EndTime,
    l.IsRecurring,
    l.RecurrenceRule
FROM Lessons l
INNER JOIN Students s ON l.StudentId = s.StudentId
WHERE l.IsArchived = 0 
    AND l.StartTime > GETDATE()
    AND s.StudentId = @StudentId  -- Replace with specific student ID
ORDER BY l.StartTime
```

## Testing

### Test Scenarios

#### Scenario 1: Regular Lessons
**Setup:**
- Student with 3 future regular lessons
- ExcludeEmail = false
- Valid email address

**Expected Result:** Student receives payment reminder

#### Scenario 2: Recurring Lessons with COUNT
**Setup:**
- Student with recurring lesson: FREQ=WEEKLY;COUNT=10
- 7 occurrences already passed
- ExcludeEmail = false

**Expected Result:** Student receives reminder for 3 remaining lessons

#### Scenario 3: Email Exclusion
**Setup:**
- Student with 2 remaining lessons
- ExcludeEmail = true

**Expected Result:** No reminder sent

#### Scenario 4: Above Threshold
**Setup:**
- Student with 5 remaining lessons
- Threshold = 3
- ExcludeEmail = false

**Expected Result:** No reminder sent

### Manual Testing

#### Test Email Template
```sql
-- Manually trigger template test
SELECT 
    'PaymentReminder' as TemplateName,
    'Test Student' as StudentName,
    '<EMAIL>' as Email,
    '2' as LessonsRemaining,
    'December 15, 2024' as PaymentDeadline
```

#### Test Lesson Counting
Use the diagnostic queries above to verify lesson counting logic for specific students.

### Automated Testing

#### Unit Tests
Create unit tests for:
- Recurrence rule parsing
- Lesson counting algorithms
- Email template placeholder replacement
- Configuration loading

#### Integration Tests
Create integration tests for:
- End-to-end reminder process
- Database interactions
- Email service integration

---

## Support and Maintenance

For technical support or questions about the Payment Reminder Service, refer to:
- Application logs for runtime issues
- Database diagnostic queries for data verification
- Configuration documentation for setup questions

Regular maintenance tasks:
- Monitor service execution logs
- Verify email template content accuracy
- Review and adjust configuration parameters based on business needs
- Test email delivery periodically
