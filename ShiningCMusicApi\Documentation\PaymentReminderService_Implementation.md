# Payment Reminder Service Implementation

## Overview

The Payment Reminder Service is a background service that automatically sends email reminders to students when they have a low number of lessons remaining, helping ensure timely payment for the next term.

## Features

- **Configurable Thresholds**: Set the number of remaining lessons that triggers a reminder
- **Email Exclusion**: Students can be excluded from automatic emails via the `ExcludeEmail` field
- **Recurring Lesson Support**: <PERSON>perly calculates remaining lessons for both regular and recurring lessons
- **Configurable Schedule**: Runs on a configurable interval (default: daily)
- **Template-Based Emails**: Uses the existing email template system

## Configuration

### Environment Variables (Recommended for Production)
- `PAYMENT_REMINDER_INTERVAL_HOURS`: How often to check for reminders (default: 24 hours)
- `PAYMENT_REMINDER_LESSON_THRESHOLD`: Number of lessons remaining to trigger reminder (default: 3)
- `PAYMENT_REMINDER_DEADLINE_DAYS`: Days from now for payment deadline (default: 7)

### appsettings.json Configuration
```json
{
  "PaymentReminder": {
    "IntervalHours": 24,
    "LessonThreshold": 3,
    "PaymentDeadlineDays": 7
  }
}
```

## Database Changes

### New Student Field
- Added `ExcludeEmail` bit field to Students table (default: false)
- Students with `ExcludeEmail = true` will not receive payment reminders

### Email Template
- New "PaymentReminder" template with placeholders:
  - `{StudentName}`: Student's name
  - `{LessonsRemaining}`: Number of lessons remaining
  - `{PaymentDeadline}`: Calculated payment deadline date

## Implementation Details

### Lesson Counting Logic
The service counts remaining lessons by:

1. **Regular Lessons**: Count future lessons (StartTime > current date)
2. **Recurring Lessons**: Calculate remaining occurrences based on:
   - UNTIL date (if specified in recurrence rule)
   - COUNT (if specified in recurrence rule)
   - For infinite series: estimate next 3 months of occurrences

### Email Sending Process
1. Query all active students (not archived, not excluded from emails)
2. Calculate remaining lessons for each student
3. Send reminders to students with lessons <= threshold
4. Log all activities for monitoring

## Setup Instructions

### 1. Run Database Migrations
```sql
-- Add ExcludeEmail field to Students table
sqlcmd -S your-server -d MusicSchool -i "SQL/Add_ExcludeEmail_Field.sql"

-- Add PaymentReminder email template
sqlcmd -S your-server -d MusicSchool -i "SQL/Add_PaymentReminder_Template.sql"
```

### 2. Configure Settings
Set environment variables or update appsettings.json with desired configuration values.

### 3. Deploy and Monitor
The service will start automatically with the application and log its activities.

## Testing

### Test Scenarios
1. **Student with 3 regular future lessons**: Should receive reminder
2. **Student with 5 lessons**: Should not receive reminder (above threshold)
3. **Student with ExcludeEmail = true**: Should not receive reminder
4. **Student with recurring lessons**: Should calculate remaining occurrences correctly
5. **Student with no email address**: Should be skipped

### Manual Testing
You can test the lesson counting logic by calling the API endpoints:
- `GET /api/lessons/student/{studentId}/remaining` (if you add this endpoint)
- Check logs for service execution details

## Monitoring

The service logs the following information:
- Service start/stop events
- Number of reminders sent each run
- Individual reminder successes/failures
- Configuration values on startup
- Any errors during processing

## Troubleshooting

### Common Issues
1. **No reminders sent**: Check if students have valid email addresses and ExcludeEmail = false
2. **Incorrect lesson counts**: Verify recurrence rule parsing for recurring lessons
3. **Email failures**: Check email service configuration and template existence
4. **Service not running**: Verify service registration in Program.cs

### Logs to Check
- PaymentReminderService logs for service-specific issues
- EmailService logs for email sending issues
- General application logs for startup/configuration issues
